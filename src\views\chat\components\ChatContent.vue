<script setup lang="ts">
import CollapseIcon from '@/components/icons/CollapseIcon.vue';
import ExpandIcon from '@/components/icons/ExpandIcon.vue';
import ImageIcon from '@/components/icons/ImageIcon.vue';
import type { ChatMessage } from '@/services/chatService';
import { computed, ref, watch } from 'vue';
import type {
  BubbleListItemProps,
  BubbleListProps
} from 'vue-element-plus-x/types/BubbleList';
import type { ImageAttachment } from './ChatInput.vue';

// Props
interface Props {
  messages: ChatMessage[];
  isAiTyping?: boolean;
  isLoadingData: boolean;
  haveMore: boolean;
  defaultCollapsedMessages?: Set<number>;
}

const props = withDefaults(defineProps<Props>(), {
  isAiTyping: false,
  isLoadingData: false,
  haveMore: true,
  defaultCollapsedMessages: () => new Set()
});

type listType = BubbleListItemProps & {
  key: number;
  role: 'user' | 'ai' | string;
  attachments?: ImageAttachment[];
};

const bubbleListRef = ref();

const loadingStyles = computed(() => ({
  opacity: props.isLoadingData ? 1 : 0,
  display: props.isLoadingData ? 'block' : 'none'
}));

const emit = defineEmits(['nextPage']); // 声明可触发的事件

// 折叠状态管理 - 使用消息ID作为key
const collapsedThinking = ref<Record<number, boolean>>({});

// 初始化折叠状态
const initializeCollapsedState = () => {
  const newCollapsedState: Record<number, boolean> = {};

  // 为所有在默认折叠列表中的消息设置为折叠状态
  props.messages.forEach(message => {
    if (props.defaultCollapsedMessages?.has(message.id)) {
      newCollapsedState[message.id] = true;
    }
  });

  // 合并现有状态，保持用户手动设置的状态
  collapsedThinking.value = { ...newCollapsedState, ...collapsedThinking.value };
};

// 监听消息变化，初始化折叠状态
watch(() => props.messages, () => {
  initializeCollapsedState();
}, { immediate: true });

// 切换思考过程折叠状态
const toggleThinking = (messageId: number) => {
  collapsedThinking.value[messageId] = !collapsedThinking.value[messageId];
};


// 将 messages 转换为 BubbleList 需要的格式
const list = computed<BubbleListProps<listType>['list']>(() => {
  const bubbleMessages: listType[] = props.messages.map((message) => {
    const role = message.type;
    const placement = role === 'ai' ? 'start' : 'end';

    return {
      key: message.id,
      role,
      placement,
      content: message.content,
      thinking: message.thinking,
      loading: message.thinkLoading,
      shape: 'corner',
      isMarkdown: role === 'ai',
      avatarSize: role === 'ai' ? '42px' : undefined,
      avatar: role === 'ai' ? 'logo.png' : undefined,
      attachments: message.attachments?.filter(item => item.status === "success"),
    };
  });

  return bubbleMessages;
});

const bubbleListScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target?.scrollTop === 0 && props.haveMore && !props.isLoadingData) {
    emit('nextPage');
  }
};

</script>
<template>
  <div class="chat-content-container">
    <div v-if="list.length === 0" class="empty-chat">
      <div class="empty-chat-logo">
        <img class="logo" src="/logo.png" alt="好兽医AI助手" />
      </div>
      <div class="empty-chat-title">欢迎使用好兽医AI助手</div>
      <div class="empty-chat-subtitle">有任何宠物健康问题，请随时向我提问</div>
    </div>

    <BubbleList :backButtonPosition="{ bottom: '18vh', left: 'calc(50% - 19px)' }" ref="bubbleListRef" :list="list"
      max-height="80dvh" class="chat-message-list" @scroll="bubbleListScroll">
      <template #header="{ item }">
        <el-card v-if="item.thinking" class="thinking-container" header-class="thinking-header"
          body-class="thinking-body" shadow="never">
          <template #header>
            <div class="thinking-header-content">
              <span style="padding-right: 24px;">{{ item.loading ? "深度思考中..." : "已经思考完毕" }}</span>
              <el-button v-if="!item.loading" size="small" class="collapse-button" @click="toggleThinking(item.key)">
                <ExpandIcon v-if="collapsedThinking[item.key]" />
                <CollapseIcon v-else />
              </el-button>
            </div>
          </template>
          <transition name="thinking-collapse" appear>
            <div v-show="!collapsedThinking[item.key]" class="thinking-content">
              {{ item.thinking }}
            </div>
          </transition>
        </el-card>
      </template>

      <template #footer="{ item }">
        <!-- 显示图片附件 -->
        <div v-if="item.attachments && item.attachments.length > 0" class="attachments-container">
          <div class="attachments-list">
            <div v-for="(attachment, index) in item.attachments" :key="index" class="attachment-item">
              <div class="attachment-icon">
                <ImageIcon />
              </div>
              <span class="attachment-name">{{ attachment.name }}</span>
              <span class="attachment-size">({{ (attachment.size / 1024).toFixed(1) }}KB)</span>
            </div>
          </div>
        </div>
        <div v-if="!(list[list.length - 1]?.key === item.key && isAiTyping) && item.role === 'ai'"
          class="footer-remark">
          <el-icon>
            <Warning />
          </el-icon>
          <Typewriter :typing="true" content="本回答由AI生成，内容仅供参考" />
        </div>
      </template>
    </BubbleList>
  </div>
</template>


<style>
/* 思考过程容器 - 灰色边框样式 */
.thinking-container {
  border-radius: 16px;
  font-size: var(--el-font-size-extra-small);
  margin-bottom: 1em;
  border: 2px solid rgba(128, 128, 128, 0.25);
  backdrop-filter: blur(12px) saturate(1.1);
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.7),
    inset 0 -2px 4px rgba(128, 128, 128, 0.12),
    inset 0 0 0 1px rgba(128, 128, 128, 0.1);
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.thinking-header {
  padding-block: calc(var(--el-card-padding) - 12px) 0px;
  border-bottom: 0px;
}

.thinking-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.collapse-button {
  padding: 6px;
  margin-left: 8px;
  color: var(--el-color-info);
  transition: all 0.3s ease;
  border-radius: 4px;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-button:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.thinking-content {
  overflow: hidden;
}

/* 思考过程折叠动画 */
.thinking-collapse-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: top left;
}

.thinking-collapse-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
  transform-origin: top left;
}

.thinking-collapse-enter-from {
  opacity: 0;
  transform: scaleY(0) scaleX(0.3);
  max-height: 0;
}

.thinking-collapse-leave-to {
  opacity: 0;
  transform: scaleY(0) scaleX(0.3);
  max-height: 0;
}

.thinking-collapse-enter-to {
  opacity: 1;
  transform: scaleY(1) scaleX(1);
  max-height: 1000px;
}

.thinking-collapse-leave-from {
  opacity: 1;
  transform: scaleY(1) scaleX(1);
  max-height: 1000px;
}

.thinking-body {
  padding-block: 0px calc(var(--el-card-padding) - 12px);
  color: var(--el-color-info);
  border-bottom: 0px;
}

.chat-content-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .el-bubble-list.chat-message-list::before {
    content: '数据加载中...';
    display: v-bind('loadingStyles.display');
    margin: auto;
    font-size: var(--el-font-size-base);
    color: var(--el-color-info);
    opacity: v-bind('loadingStyles.opacity');
    transition: opacity 0.3s ease-out;
  }

  .el-bubble-list.chat-message-list::after {
    content: "想养龙鱼，但是家里不让买鱼缸";
    display: block;
    width: 100%;
    line-height: 25dvh;
    opacity: 0;
  }
}

.chat-message-list {
  padding-top: 12px;

  .el-avatar.el-avatar--circle {
    background: transparent;

    >img {
      background: var(--logo-background);
    }
  }

  /* 用户消息气泡 - 无外阴影的立体设计 */
  .el-bubble.el-bubble-end .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled {
    background:
      linear-gradient(135deg,
        rgba(0, 87, 255, 0.76) 0%,
        rgba(0, 120, 255, 0.89) 30%,
        rgba(0, 150, 255, 0.86) 70%,
        rgba(0, 87, 255, 0.76) 100%),
      linear-gradient(45deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.08) 100%);
    color: #ffffff;
    border: 2px solid rgba(0, 87, 255, 0.25);
    backdrop-filter: blur(12px) saturate(1.1);
    box-shadow:
      inset 0 2px 4px rgba(255, 255, 255, 0.5),
      inset 0 -2px 4px rgba(0, 87, 255, 0.2),
      inset 0 0 0 1px rgba(0, 87, 255, 0.15);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    text-shadow: 0 1px 3px rgba(0, 87, 255, 0.4);
    transform: translateZ(0);
  }

  /* 用户消息气泡微光效果 - 与AI消息呼应 */
  .el-bubble.el-bubble-end .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.15) 0%,
        rgba(255, 255, 255, 0.08) 50%,
        rgba(255, 255, 255, 0.15) 100%);
    border-radius: inherit;
    opacity: 0.6;
    pointer-events: none;
  }

  /* 用户消息气泡顶部光线 - 与AI消息统一 */
  .el-bubble.el-bubble-end .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.5),
        transparent);
    opacity: 0.8;
  }

  /* AI消息内容容器 - 无外阴影的玻璃态设计 */
  .el-bubble.el-bubble-start .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled {
    background:
      linear-gradient(135deg,
        rgba(0, 87, 255, 0.06) 0%,
        rgba(0, 150, 255, 0.10) 50%,
        rgba(0, 87, 255, 0.06) 100%),
      linear-gradient(45deg,
        rgba(255, 255, 255, 0.85) 0%,
        rgba(240, 248, 255, 0.90) 100%);
    border: 2px solid rgba(0, 87, 255, 0.25);
    backdrop-filter: blur(12px) saturate(1.1);
    box-shadow:
      inset 0 2px 4px rgba(255, 255, 255, 0.7),
      inset 0 -2px 4px rgba(0, 87, 255, 0.12),
      inset 0 0 0 1px rgba(0, 87, 255, 0.1);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
  }

  /* AI消息内容发光边框效果 */
  .el-bubble.el-bubble-start .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(0, 87, 255, 0.05) 0%,
        rgba(0, 150, 255, 0.08) 50%,
        rgba(0, 87, 255, 0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    pointer-events: none;
  }

  /* AI消息内容顶部光线效果 */
  .el-bubble.el-bubble-start .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 87, 255, 0.4),
        transparent);
    opacity: 0.8;
  }

  p {
    margin-block: 0px;
  }

  .el-bubble-content.el-bubble-content-corner.el-bubble-content-filled {
    max-width: 100%
  }

  code {
    white-space: pre-wrap;
  }
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-bottom: 20dvh;
  color: #666;
  text-align: center;
}

.logo {
  background: var(--logo-background);
  border-radius: 50%;
}

.empty-chat-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.empty-chat-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.empty-chat-subtitle {
  font-size: 14px;
  color: #999;
}

/* 附件样式 */
.attachments-container {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
  font-size: 13px;
  border: var(--el-border-color) 1px solid;
}

.attachment-icon {
  color: var(--el-color-primary);
  font-size: 14px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.attachment-name {
  color: var(--el-color-black);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.attachment-size {
  color: var(--el-text-color-secondary);
  font-size: 12px;
  flex-shrink: 0;
}

/* 底部提示信息 - 橙色系设计 */
.footer-remark {
  color: rgba(255, 140, 0, 0.8);
  font-size: var(--el-font-size-extra-small);
  margin-top: 0px;
  background:
    linear-gradient(135deg,
      rgba(255, 140, 0, 0.06) 0%,
      rgba(255, 165, 0, 0.10) 50%,
      rgba(255, 140, 0, 0.06) 100%),
    linear-gradient(45deg,
      rgba(255, 255, 255, 0.85) 0%,
      rgba(255, 248, 240, 0.90) 100%);
  border: 2px solid rgba(255, 140, 0, 0.25);
  backdrop-filter: blur(12px) saturate(1.1);
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.7),
    inset 0 -2px 4px rgba(255, 140, 0, 0.12),
    inset 0 0 0 1px rgba(255, 140, 0, 0.1);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  font-size: var(--el-font-size-extra-small);
  gap: 4px;
}

/* 底部提示顶部光线效果 */
.footer-remark::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 140, 0, 0.4),
      transparent);
  opacity: 0.8;
}
</style>
