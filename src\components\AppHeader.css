/* 导航栏 */
.navbar {
  position: fixed;
  top: 1.5dvw;
  right: 3dvw;
  z-index: 1000;
  padding: 0.8dvw 2.4dvw;
  border: 0.18dvw solid white;
  border-radius: 2dvw;
  background-color: rgba(170, 170, 170, 0.35);
  backdrop-filter: blur(10px);
}

.nav-links {
  display: flex;
  gap: 1.5dvw;
  a {
    color: #fff;
    text-shadow: 0 1px 6px rgba(60, 0, 80, 0.2), 0 0 2px #6ecbff;
    text-decoration: none;
    font-size: 0.9dvw;
    font-weight: b;
  }
}
