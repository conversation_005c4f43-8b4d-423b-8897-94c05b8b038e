<script setup lang="ts">
import AppHeader from './components/header/Header.vue';
import { logoBackground } from './constants/styles';
</script>

<template>
  <div class="page-container" :style="{ '--logo-background': logoBackground }">
    <div class="page-header">
      <AppHeader />
    </div>
    <div class="page-content">
      <router-view />
    </div>
  </div>
</template>

<style scoped></style>

<style>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100dvh;
}

.page-header {
  flex: 0;
}

.page-content {
  flex: 1;
  padding-inline: 12px;
}

body {
  margin: 0;
}

/* 全局隐藏滚动条但保持滚动功能 */
* {
  /* 隐藏 Webkit 浏览器的滚动条 */
  scrollbar-width: none !important;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

*::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}
</style>
