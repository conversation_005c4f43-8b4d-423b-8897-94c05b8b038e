<template>
  <div class="initial-loading-overlay">
    <div class="loading-container">
      <!-- Logo with glow effect -->
      <div class="logo-container">
        <div class="logo-glow"></div>
        <img src="/logo.png" alt="好兽医AI助手" class="logo" />
        <div class="pulse-ring"></div>
        <div class="pulse-ring-2"></div>
      </div>

      <!-- Loading text -->
      <div class="loading-text">
        <div class="main-text">好兽医AI助手</div>
        <div class="sub-text">正在为您准备聊天历史...</div>
      </div>

      <!-- Animated dots -->
      <div class="loading-dots">
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
      </div>

      <!-- Tech grid background -->
      <div class="tech-grid"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 初始加载动画组件
</script>

<style scoped>
/* 关键帧动画 */
@keyframes logoGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes pulseRing2 {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }

  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes textFade {

  0%,
  100% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }
}

@keyframes dotBounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  40% {
    transform: translateY(-10px);
    opacity: 1;
  }

  60% {
    transform: translateY(-5px);
    opacity: 0.8;
  }
}

@keyframes floatPaw {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }

  50% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 0.6;
  }

  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
}

@keyframes techGrid {
  0% {
    opacity: 0.1;
  }

  50% {
    opacity: 0.3;
  }

  100% {
    opacity: 0.1;
  }
}

/* 主要样式 */
.initial-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 40%, #001f66 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 10;
}

/* Logo 样式 */
.logo-container {
  position: relative;
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  background: var(--logo-background);
  object-fit: contain;
  box-shadow:
    0 0 30px rgba(255, 255, 255, 0.6),
    0 0 50px rgba(0, 87, 255, 0.4),
    0 0 70px rgba(0, 87, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 3;
  animation: logoGlow 2s infinite ease-in-out;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 2;
  animation: logoGlow 2s infinite ease-in-out;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
  z-index: 1;
  animation: pulseRing 2s infinite ease-out;
}

.pulse-ring-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  z-index: 1;
  animation: pulseRing2 2s infinite ease-out 0.5s;
}

/* 文字样式 */
.loading-text {
  margin-bottom: 25px;
}

.main-text {
  font-size: 28px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
  letter-spacing: 2px;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  text-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  animation: textFade 3s infinite ease-in-out;
}

.sub-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
  animation: textFade 3s infinite ease-in-out 0.5s;
}

/* 加载点动画 */
.loading-dots {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.6), 0 0 25px rgba(0, 87, 255, 0.3);
}

.dot-1 {
  animation: dotBounce 1.5s infinite ease-in-out;
}

.dot-2 {
  animation: dotBounce 1.5s infinite ease-in-out 0.2s;
}

.dot-3 {
  animation: dotBounce 1.5s infinite ease-in-out 0.4s;
}

/* 科技网格背景 */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
  animation: techGrid 4s infinite ease-in-out;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .logo {
    width: 60px;
    height: 60px;
  }

  .main-text {
    font-size: 24px;
  }

  .sub-text {
    font-size: 14px;
  }
}
</style>
