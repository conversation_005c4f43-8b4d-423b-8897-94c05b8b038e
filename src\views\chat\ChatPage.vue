<script setup lang="ts">
import InitialLoading from '@/components/loading/InitialLoading.vue';
import type { ChatMessage } from '@/services/chatService';
import ChatService from '@/services/chatService';
import ConversationService from '@/services/conversationService';
import { onMounted, onUnmounted, ref } from 'vue';
import ChatContent from './components/ChatContent.vue';
import type { ImageAttachment } from './components/ChatInput.vue';
import ChatInput from './components/ChatInput.vue';

const messages = ref<ChatMessage[]>([]);
const isAiTyping = ref(false);
const currentPage = ref(1);
const isLoadingData = ref(false);
const haveMore = ref(true);
const isInitialLoading = ref(true);

// 页面可见性状态管理
let visibilityChangeHandler: (() => void) | null = null;

// 添加消息
const addMessage = (message: string, type: 'user' | 'ai' = 'user', attachments?: ImageAttachment[]) => {
  const newMessage: ChatMessage = {
    id: Date.now() + Math.random(),
    type,
    content: message,
    timestamp: Date.now(),
    loading: type === 'ai',
    thinkLoading: type === 'ai',
    attachments
  };

  messages.value.push(newMessage);
  return newMessage;
};

function handleErrorMessage(msg: string = "输出已中断，请重新发送消息") {
  const lastAiMessage = messages.value.slice().reverse().find(msg => msg.type === 'ai');
  if (lastAiMessage) {
    lastAiMessage.loading = false;
    lastAiMessage.thinkLoading = false;
    if (!lastAiMessage.content) {
      lastAiMessage.content = msg
    }
  }
}

// 中止AI回复
const stopAiResponse = () => {
  // 先中止请求
  ChatService.abortCurrentRequest();

  // 找到最后一条AI消息并停止其加载状态
  handleErrorMessage()

  // 添加一个小的延迟，确保点击事件完全处理完成后再更新状态
  // 这样可以避免 v-if 导致的按钮过早销毁问题
  setTimeout(() => {
    isAiTyping.value = false;
  }, 10); // 10ms 的延迟足够确保事件处理完成
};

// 发送用户消息
const sendMessage = async (message: string, attachments?: ImageAttachment[]) => {

  if (!message.trim() && (!attachments || attachments.length === 0)) return;

  //检查会话
  ConversationService.checkConversation();

  // 添加用户消息
  addMessage(message, 'user', attachments);

  // 设置AI正在输入状态
  isAiTyping.value = true;

  try {
    // 创建一个空的 AI 回复消息，用于流式更新
    const aiMessage = addMessage('', 'ai');

    // 使用流式响应
    await ChatService.sendMessageStream(
      {
        message: message,
        attachments
      },
      (chunk) => {
        // 更新消息内容
        updateMessage(aiMessage.id, chunk.content, chunk.thinking, chunk.finished, chunk.thinkFinished);
      }
    );
  } catch (error) {
    console.error('发送消息失败:', error);
    handleErrorMessage('抱歉，我现在无法回复您的消息，请稍后再试。');
  } finally {
    isAiTyping.value = false;
  }
};

// 更新消息内容（用于流式响应）
const updateMessage = (messageId: number, content: string, thinking: string, finished: boolean = false, thinkFinished: boolean = false) => {
  const messageIndex = messages.value.findIndex(msg => msg.id === messageId);
  if (messageIndex !== -1) {
    messages.value[messageIndex].content = content;
    messages.value[messageIndex].thinking = thinking;
    if (finished) {
      messages.value[messageIndex].loading = false;
    }
    if (thinkFinished) {
      messages.value[messageIndex].thinkLoading = false;
    }
  }
};

// 存储需要默认折叠的消息ID
const defaultCollapsedMessages = ref<Set<number>>(new Set());

const loadChatHistory = async () => {
  try {
    isLoadingData.value = true

    // 确保加载动画至少显示x秒
    const startTime = Date.now();
    const minLoadingTime = 1500;

    const { items: history } = await ChatService.getChatHistory(currentPage.value);

    if (history && history.length > 0) {
      const oldMessageIds = messages.value.map((item) => item.id)
      const messageList: ChatMessage[] = history
        .filter((item: any) => !oldMessageIds.includes(item.id))
        .map((item: any) => {
          const contentList = item.content.split("</think>")
          const messageId = item.id;

          // 如果消息有思考过程，将其ID添加到默认折叠列表中
          if (contentList.length > 1) {
            defaultCollapsedMessages.value.add(messageId);
          }

          return {
            id: messageId,
            type: item.role === 1 ? 'user' : 'ai',
            content: contentList.length > 1 ? contentList[1] : contentList[0],
            thinking: contentList.length > 1 ? contentList[0] : undefined,
            loading: false,
            thinkLoading: false
          }
        }).reverse()
      messages.value = [...messageList, ...messages.value];
      currentPage.value += 1
    } else {
      haveMore.value = false
    }

    // 确保最小加载时间
    const elapsedTime = Date.now() - startTime;
    if (elapsedTime < minLoadingTime) {
      await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
    }
  } finally {
    isLoadingData.value = false
    isInitialLoading.value = false
  }
};
// 页面可见性变化处理
const handleVisibilityChange = () => {
  const isVisible = !document.hidden;
  if (!isVisible && isAiTyping.value) {
    // 中止当前的流式请求
    ChatService.abortCurrentRequest();
    handleErrorMessage('输出已中断，请重新发送消息');
    isAiTyping.value = false;
  }
};

// 组件挂载时加载聊天历史和设置页面可见性监听
onMounted(() => {
  ConversationService.regenerateConversation().then(() => {
    loadChatHistory();
  });

  // 添加页面可见性变化监听
  visibilityChangeHandler = handleVisibilityChange;
  document.addEventListener('visibilitychange', visibilityChangeHandler);
});

// 组件卸载时清理监听器
onUnmounted(() => {
  if (visibilityChangeHandler) {
    document.removeEventListener('visibilitychange', visibilityChangeHandler);
    visibilityChangeHandler = null;
  }
});

</script>

<template>
  <!-- 初始加载动画 -->
  <InitialLoading v-if="isInitialLoading" />
  <!-- 聊天界面 -->
  <div v-else class="chat-container">
    <ChatContent :messages="messages" :is-ai-typing="isAiTyping" :is-loading-data="isLoadingData" :have-more="haveMore"
      :default-collapsed-messages="defaultCollapsedMessages" @nextPage="loadChatHistory" />
    <ChatInput @send-message="sendMessage" @stop-response="stopAiResponse" :disabled="isAiTyping"
      :is-ai-typing="isAiTyping" />
  </div>
</template>

<style>
.chat-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
</style>
