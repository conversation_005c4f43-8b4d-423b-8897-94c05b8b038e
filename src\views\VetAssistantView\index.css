.vet-assistant-view {
  background: url("/home/<USER>") no-repeat center center;
  background-size: cover;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  > h1 {
    font-size: var(--h1-font-middle-size);
    letter-spacing: 0.6dvw;
  }
  > p {
    font-size: 1dvw;
    line-height: 1.5dvw;
    letter-spacing: 0.15dvw;
    width: 40%;
    margin: 0;
    animation: fade-in-title 1.4s cubic-bezier(0.4,2,0.6,1) 0.7s both;
  }
}

.trial-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.2dvw;
  border-radius: 2vw;
  border: 3px solid white;
  color: white;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(255, 165, 0, 0.3) 0%,
    rgba(255, 140, 0, 0.3) 50%,
    rgba(255, 69, 0, 0.3) 100%
  );
  backdrop-filter: blur(10px);
  box-shadow: 0 0 15px rgba(255, 140, 0, 0.5);
  transition: all 0.3s ease;
  margin-block: 4dvh 10dvh;
  animation: fade-in-title 1.2s cubic-bezier(0.4,2,0.6,1) 1.2s both;
}

.trial-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 180, 0, 0.5) 0%,
    rgba(255, 150, 0, 0.5) 50%,
    rgba(255, 90, 0, 0.5) 100%
  );
  box-shadow: 0 0 20px rgba(255, 140, 0, 0.8);
  transform: translateY(-2px);
}

.assistant-title {
  display: inline-block;
  white-space: nowrap;
}
.assistant-title .title-main {
  display: inline-block;
  opacity: 0;
  animation: fade-in-title 1.8s cubic-bezier(0.4,2,0.6,1) 0.1s both;
}
@keyframes fade-in-title {
  0% {
    opacity: 0;
    filter: blur(12px);
  }
  100% {
    opacity: 1;
    filter: blur(0);
  }
}