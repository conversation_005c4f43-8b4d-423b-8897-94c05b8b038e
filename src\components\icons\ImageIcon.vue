<template>
  <svg 
    class="icon" 
    viewBox="0 0 1024 1024" 
    version="1.1" 
    xmlns="http://www.w3.org/2000/svg"
    width="1em" 
    height="1em"
  >
    <path 
      d="M32 112.20000031C32 67.92000031 67.88 32 112.20000031 32h799.59999938C956.07999969 32 992 67.88 992 112.20000031v799.59999938A80.20000031 80.20000031 0 0 1 911.79999969 992H112.20000031A80.20000031 80.20000031 0 0 1 32 911.79999969V112.20000031zM752 392a60 60 0 1 0 0-120 60 60 0 0 0 0 120z m42 360c17.04 0 23.08000031-14.71999969 13.36000031-28.27999969l-227.60000062-321.12c-9.72-13.56-26.47999969-14.16-37.35999938-1.52000062l-87.52000031 102.52000031a28.24000031 28.24000031 0 0 1-41.59999969 2.08000031l-36.40000031-34.92c-12.07999969-11.64-29.92000031-9.19999969-39.76000031 4.2l-180.39999938 248.91999938c-9.79999969 13.48000031-3.88000031 28.12000031 13.2 28.12000031h624.03999938z" 
      fill="currentColor"
    />
  </svg>
</template>

<script setup lang="ts">
// 这个组件不需要任何props或逻辑
</script>

<style scoped>
.icon {
  display: inline-block;
  vertical-align: middle;
}
</style>
