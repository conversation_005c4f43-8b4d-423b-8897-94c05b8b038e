import api from "./api"

/**
 * 获取 token
 */
export const getToken = (): string | null => {
  return localStorage.getItem('token') || sessionStorage.getItem('token')
}

/**
 * 设置 token
 */
export const setToken = (token: string, remember: boolean = false): void => {
  if (remember) {
    localStorage.setItem('token', token)
  } else {
    sessionStorage.setItem('token', token)
  }
  api.setHeader('Authorization', `Bear<PERSON> ${token}`)
}

/**
* 获取 会话ID
*/
export const getConversationId = (): string | null => {
  return sessionStorage.getItem('conversation_id')
}

/**
* 设置 会话ID
*/
export const setConversationId = (conversation_id: string): void => {
  sessionStorage.setItem('conversation_id', conversation_id)
}

/**
* 删除 会话ID
*/
export const removeConversationId = (): void => {
  sessionStorage.removeItem('conversation_id');
};

