.vet-open-view {
  background: url("/home/<USER>") no-repeat center center;
  background-size: cover;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  > h1 {
    font-size: var(--h1-font-middle-size);
    letter-spacing: 0.6dvw;
  }
  > p {
    font-size: 1dvw;
    line-height: 1.5dvw;
    letter-spacing: 0.15dvw;
    width: 40%;
  }
}

.trial-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.2dvw;
  border-radius: 2vw;
  border: 3px solid white;
  color: white;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(255, 102, 204, 0.3) 0%,
    rgba(204, 102, 255, 0.3) 50%,
    rgba(255, 153, 255, 0.3) 100%
  );
  backdrop-filter: blur(10px);
  box-shadow: 0 0 15px rgba(255, 102, 204, 0.5);
  transition: all 0.3s ease;
  margin-block: 4dvh 10dvh;
}

.trial-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 102, 204, 0.5) 0%,
    rgba(204, 102, 255, 0.5) 50%,
    rgba(255, 153, 255, 0.5) 100%
  );
  box-shadow: 0 0 20px rgba(204, 102, 255, 0.8);
  transform: translateY(-2px);
}
