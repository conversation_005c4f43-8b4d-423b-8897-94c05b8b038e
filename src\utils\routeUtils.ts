/**
 * 路由参数类型定义
 */
export interface RouteParams {
  api_key: string;
  report_id: string;
}

/**
 * 从当前 URL 解析查询参数（不依赖 Vue Router）
 * 查询参数格式: ?q=xxxxxxxx
 * q=btoa('{"api_key":"sk-proj-Nmtw7HENVQiibyAS8VatGESe5LXTLO5P64ufg2IKEjgTyleu","report_id":2525}')
 */
export const parseRouteParamsFromUrl = (): RouteParams => {
  const urlParams = new URLSearchParams(window.location.search);
  const q = urlParams.get("q");
  let api_key = ""
  let report_id = ""
  if (q) {
    const params: RouteParams = JSON.parse(atob(q));
    api_key = params.api_key;
    report_id = params.report_id;
  }

  return {
    api_key: api_key,
    report_id: report_id,
  };
};

/**
 * 从路由参数构建 Authorization header（可在任何地方使用）
 */
export const getAuthorizationHeader = (): string => {
  const params = parseRouteParamsFromUrl();
  if (!params) {
    throw new Error('Unable to parse API key from URL');
  }
  return `Bearer ${params.api_key}`;
};

/**
 * 获取报告 ID（可在任何地方使用）
 */
export const getReportId = (): string => {
  const params = parseRouteParamsFromUrl();
  if (!params) {
    throw new Error('Unable to parse report ID from URL');
  }
  return params.report_id;
};

/**
 * 获取 API Key（可在任何地方使用）
 */
export const getApiKey = (): string => {
  const params = parseRouteParamsFromUrl();
  if (!params) {
    throw new Error('Unable to parse API key from URL');
  }
  return params.api_key;
};

/**
 * 从路由参数构建 Authorization header（可在任何地方使用）
 */
export const replaceToNotPermission = (): void => {
  window.location.replace("/not-permission");
};

