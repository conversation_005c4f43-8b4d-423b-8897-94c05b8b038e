<template>
  <div class="not-found-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
      </div>
      <div class="tech-grid"></div>
    </div>

    <!-- 主要内容 -->
    <div class="content-wrapper">
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-glow"></div>
          <img src="/logo.png" alt="好兽医AI助手" class="logo" />
          <div class="pulse-ring"></div>
          <div class="pulse-ring-2"></div>
        </div>
      </div>

      <!-- 404信息 -->
      <div class="error-info">
        <div class="error-code">404</div>
        <div class="error-title">页面走丢了</div>
        <div class="error-subtitle">抱歉，您访问的页面不存在或已被移除</div>

        <!-- 建议操作 -->
        <div class="suggestions">
          <div class="suggestion-item">
            <div class="suggestion-icon">🏠</div>
            <div class="suggestion-text">尝试其他方式访问</div>
          </div>
          <div class="suggestion-item">
            <div class="suggestion-icon">💬</div>
            <div class="suggestion-text">联系相关工作人员</div>
          </div>
          <div class="suggestion-item">
            <div class="suggestion-icon">🔍</div>
            <div class="suggestion-text">检查网址是否正确</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped>
/* 关键帧动画 */
@keyframes logoGlow {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes pulseRing {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }

  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes pulseRing2 {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.6;
  }

  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes floatCircle {

  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }

  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes techGrid {

  0%,
  100% {
    opacity: 0.1;
  }

  50% {
    opacity: 0.3;
  }
}

@keyframes petFloat {

  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.4;
  }

  50% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 0.7;
  }
}

@keyframes errorCodeGlow {

  0%,
  100% {
    text-shadow: 0 0 20px rgba(0, 87, 255, 0.5);
  }

  50% {
    text-shadow: 0 0 40px rgba(0, 87, 255, 0.8);
  }
}

/* 主容器 */
.not-found-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(0, 87, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.95) 25%,
      rgba(255, 255, 255, 0.98) 75%,
      rgba(0, 87, 255, 0.03) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 87, 255, 0.1) 0%, rgba(0, 87, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 15%;
  left: 10%;
  animation: floatCircle 6s infinite ease-in-out;
}

.circle-2 {
  width: 60px;
  height: 60px;
  top: 25%;
  right: 15%;
  animation: floatCircle 8s infinite ease-in-out 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation: floatCircle 7s infinite ease-in-out 4s;
}

.circle-4 {
  width: 70px;
  height: 70px;
  bottom: 30%;
  right: 10%;
  animation: floatCircle 9s infinite ease-in-out 1s;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 87, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 87, 255, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: techGrid 8s infinite ease-in-out;
}

/* 主要内容 */
.content-wrapper {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600px;
  padding: 40px 20px;
}

/* Logo区域 */
.logo-section {
  margin-block: 40px;
}

.logo-container {
  position: relative;
  display: inline-block;
}

.logo {
  width: 100px;
  height: 100px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.95);
  object-fit: contain;
  box-shadow:
    0 0 30px rgba(255, 255, 255, 0.6),
    0 0 50px rgba(0, 87, 255, 0.4),
    0 0 70px rgba(0, 87, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 3;
  animation: logoGlow 3s infinite ease-in-out;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(0, 87, 255, 0.3) 0%, rgba(0, 87, 255, 0) 70%);
  border-radius: 50%;
  z-index: 2;
  animation: logoGlow 3s infinite ease-in-out;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  border: 2px solid rgba(0, 87, 255, 0.4);
  z-index: 1;
  animation: pulseRing 3s infinite ease-out;
}

.pulse-ring-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  border: 1px solid rgba(0, 87, 255, 0.3);
  z-index: 1;
  animation: pulseRing2 3s infinite ease-out 1s;
}

/* 错误信息 */
.error-info {
  margin-bottom: 40px;
}

.error-code {
  font-size: 120px;
  font-weight: 900;
  color: #0057ff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: errorCodeGlow 3s infinite ease-in-out;
  line-height: 1;
}

.error-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
  letter-spacing: 1px;
}

.error-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
  max-width: 400px;
}

/* 建议操作 */
.suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 30px;
  max-width: 400px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 87, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  background: rgba(0, 87, 255, 0.05);
  border-color: rgba(0, 87, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.1);
}

.suggestion-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.suggestion-text {
  font-size: 14px;
  color: #555;
  font-weight: 500;
  text-align: left;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 20px 16px;
    max-width: 100%;
  }

  .logo {
    width: 80px;
    height: 80px;
    background: var(--logo-background);
  }

  .logo-glow {
    width: 100px;
    height: 100px;
  }

  .pulse-ring,
  .pulse-ring-2 {
    width: 90px;
    height: 90px;
  }

  .error-code {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-subtitle {
    font-size: 14px;
  }

  .suggestions {
    width: 100%;
  }

  .floating-circle {
    opacity: 0.5;
  }

  .circle-1,
  .circle-3 {
    width: 60px;
    height: 60px;
  }

  .circle-2,
  .circle-4 {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 60px;
  }

  .error-title {
    font-size: 20px;
  }

  .logo {
    width: 60px;
    height: 60px;
  }

  .suggestion-item {
    padding: 10px 12px;
  }

  .suggestion-text {
    font-size: 13px;
  }
}
</style>
