import api from '@/utils/api';
import { getAuthorizationHeader } from '@/utils/routeUtils';

// OCR 请求参数
export interface OcrRequest {
  image: string // base64编码的图片
  // 其他参数可以根据API需求添加
}

// OCR 响应数据
export interface OcrResponse {
  success: boolean;
  result: {
    text: string;
    confidence: number;
    language: string;
    processing_time_ms: number;
    bounding_boxes: [];
    metadata: {
      model: string;
      usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
      };
      finish_reason: string;
    };
  };
  processing_time_ms: number;
  error: string
}

/**
 * OCR 服务类
 */
export class OcrService {
  /**
   * 提取图片中的文字
   * @param imageAttachment 图片附件
   * @returns OCR识别结果
   */
  static async ocrText(imageBase64: string): Promise<OcrResponse> {
    const response = await api.post<OcrResponse>('api/ocr/extract', {
      image_base64: imageBase64
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': getAuthorizationHeader()
      }
    })
    return response
  }

  /**
   * 验证图片格式和大小
   * @param file 文件对象
   * @returns 验证结果
   */
  static validateImage(file: File): { valid: boolean; message?: string } {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        message: '不支持的图片格式，请上传 JPG、PNG、GIF 或 WebP 格式的图片'
      }
    }

    // 检查文件大小 (限制为5MB)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      return {
        valid: false,
        message: '图片大小不能超过5MB'
      }
    }

    return { valid: true }
  }

  /**
   * 将文件转换为base64
   * @param file 文件对象
   * @returns base64字符串
   */
  static fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除data:image/xxx;base64,前缀，只保留base64数据
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }
}

// 默认导出
export default OcrService
