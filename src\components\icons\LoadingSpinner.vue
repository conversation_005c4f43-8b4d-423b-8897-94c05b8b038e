<template>
  <span class="loading-spinner" v-html="iconSvg"></span>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants/icons';

const iconSvg = ICONS.LOADING_SPINNER;
</script>

<style scoped>
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

.loading-spinner :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
