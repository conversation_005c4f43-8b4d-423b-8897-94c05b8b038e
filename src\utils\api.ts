import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  AxiosError,
} from "axios";

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
}

// 错误处理类型
export interface ApiError {
  code: number;
  message: string;
  data?: unknown;
}

class ApiClient {
  private instance: AxiosInstance;
  private baseURL: string;
  private timeout: number;
  private loadingCount = 0;

  constructor(baseURL = "", timeout = 60000) {
    this.baseURL = baseURL;
    this.timeout = timeout;

    // 创建 axios 实例
    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // 设置请求拦截器
    this.setupRequestInterceptor();

    // 设置响应拦截器
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.instance.interceptors.request.use(
      async (config) => {
        // 显示加载状态
        if ((config as RequestConfig).showLoading !== false) {
          this.showLoading();
        }

        // 添加时间戳防止缓存
        if (config.method === "get") {
          config.params = {
            ...config.params,
            _t: Date.now(),
          };
        }

        return config;
      },
      (error) => {
        this.hideLoading();
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.instance.interceptors.response.use(
      (response) => {
        this.hideLoading();
        const { data, status } = response;
        // 检查业务状态码
        if (data.code === 200 || data.success || status === 200) {
          return data;
        } else {
          // 业务错误处理
          const error: ApiError = {
            code: data.code,
            message: data.message || "请求失败",
            data: data.data,
          };
          return Promise.reject(error);
        }
      },
      (error: AxiosError) => {
        // 请求错误处理
        this.hideLoading();
        return Promise.reject(error);
      }
    );
  }

  /**
   * 显示加载状态
   */
  private showLoading(): void {
    this.loadingCount++;
    // 这里可以集成你的全局 loading 组件
    // 例如：store.commit('setLoading', true)
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(): void {
    this.loadingCount--;
    if (this.loadingCount <= 0) {
      this.loadingCount = 0;
      // 这里可以集成你的全局 loading 组件
      // 例如：store.commit('setLoading', false)
    }
  }

  /**
   * GET 请求
   */
  public get<T = unknown>(
    url: string,
    params?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.get(url, { ...config, params });
  }

  /**
   * POST 请求
   */
  public post<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.post(url, data, config);
  }

  /**
   * PUT 请求
   */
  public put<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.put(url, data, config);
  }

  /**
   * DELETE 请求
   */
  public delete<T = unknown>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config);
  }

  /**
   * PATCH 请求
   */
  public patch<T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<T> {
    return this.instance.patch(url, data, config);
  }

  /**
   * 上传文件
   */
  public upload<T = unknown>(
    url: string,
    file: File,
    config?: RequestConfig
  ): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    return this.instance.post(url, formData, {
      ...config,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 下载文件
   */
  public download(
    url: string,
    filename?: string,
    config?: RequestConfig
  ): Promise<void> {
    return this.instance
      .get(url, {
        ...config,
        responseType: "blob",
      })
      .then((response) => {
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename || "download";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      });
  }

  /**
   * 设置默认请求头
   */
  public setHeader(key: string, value: string): void {
    this.instance.defaults.headers.common[key] = value;
  }

  /**
   * 移除请求头
   */
  public removeHeader(key: string): void {
    delete this.instance.defaults.headers.common[key];
  }
}

// 创建默认实例
const api = new ApiClient();

export default api;
export { ApiClient };
