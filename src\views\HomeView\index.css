.home-view {
  display: flex;
  flex-direction: column;
}

.home-page-1 {
  background: url("/home/<USER>") no-repeat center center;
  background-size: cover;
  display: flex;
  height: 100dvh;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  letter-spacing: 0.2dvw;
  > h1 {
    font-weight: bold;
    font-size: var(--h1-font-large-size);
  }
  > h2 {
    font-weight: bold;
    font-size: 1.5dvw;
    color: var(--el-text-color-regular);
    margin-bottom: 4dvh;
  }
  > span {
    font-weight: bold;
    font-size: 1dvw;
    display: flex;
    align-items: center;
    color: var(--el-text-color-regular);
    > img {
      width: 2.5dvw;
    }
  }
}

.home-page-1 > h2 {
  animation: fade-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 0.8s both;
}
.home-page-1 > span {
  animation: fade-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 1.2s both;
}

@keyframes fade-in-down {
  0% {
    opacity: 0;
    transform: translateY(-2vw);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.home-page-2 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100dvh;
  padding-inline: 22dvw;
  line-height: 3dvw;
  letter-spacing: 0.1dvw;
  font-size: 1.6dvw;
  font-weight: bold;
  color: var(--el-text-color-regular);
  > p {
    text-indent: 1.6dvw;
  }
}

.home-page-3 {
  background-size: cover;
  background: linear-gradient(
    to bottom right,
    rgb(217 148 100) 0%,
    rgb(199 142 147) 30%,
    rgb(96 114 203) 50%,
    rgb(64 150 162) 100%
  );
  > div {
    height: 100dvh;
    display: flex;
    gap: 3dvw;
    justify-content: center;
    align-items: center;
    width: 80dvw;
    margin: auto;
    > img {
      width: inherit;
      border-radius: var(--el-border-radius-round);
      border: 2px solid rgb(255, 255, 255);
      flex: 0;
      width: 30dvw;
    }
    > span {
      flex: 1;
      color: white;
      letter-spacing: 0.1dvw;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      h5 {
        font-size: 2.5dvw;
        margin-block: 0px 3dvh;
      }
      p {
        margin: 0.2rem;
        font-size: 1.2dvw;
        line-height: 1.7dvw;
      }
      aside.human-aside-start {
        font-size: 3.2dvw;
        transform: translateY(3dvh);
      }
      aside.human-aside-end {
        font-size: 3.2dvw;
        text-align: right;
      }
    }
  }
}

.trial-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.2dvw;
  border-radius: 2vw;
  border: 2.5px solid #a0aaff;
  color: #fff;
  cursor: pointer;
  background: linear-gradient(
    100deg,
    rgba(110, 203, 255, 0.65) 0%,
    rgba(179, 136, 255, 0.55) 70%,
    rgba(255, 138, 226, 0.6) 100%
  );
  box-shadow:
    0 0 18px 0 rgba(110, 203, 255, 0.18),
    0 0 32px 0 rgba(255, 138, 226, 0.12);
  font-weight: 900;
  letter-spacing: 0.1em;
  text-shadow:
    0 2px 12px rgba(60, 0, 80, 0.28),
    0 1px 0 #fff,
    0 0 8px #b388ff;
  transition: all 0.3s cubic-bezier(0.4, 2, 0.6, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(16px) saturate(160%);
  -webkit-backdrop-filter: blur(16px) saturate(160%);
  background-clip: padding-box;
  border: 2.5px solid rgba(160, 170, 255, 0.35);
  outline: 1.5px solid rgba(255, 255, 255, 0.18);
  margin-block: 4dvh 10dvh;
}
.trial-button::before {
  content: "";
  position: absolute;
  left: -50%;
  top: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(60, 0, 80, 0.1) 0%,
    rgba(255, 138, 226, 0.1) 100%
  );
  z-index: 0;
  pointer-events: none;
}
.trial-button:hover {
  box-shadow:
    0 0 32px 0 rgba(179, 136, 255, 0.22),
    0 0 48px 0 rgba(110, 203, 255, 0.18);
  transform: translateY(-2px) scale(1.04);
}
.trial-button:active {
  filter: brightness(0.95);
  transform: scale(0.98);
}

.animated-title {
  display: inline-block;
  white-space: pre;
}
.animated-title .char {
  display: inline-block;
  opacity: 0;
  transform: translate(var(--x, 0), var(--y, 0)) scale(1.8) rotate(var(--r, 0deg));
  animation: fly-in 1.2s cubic-bezier(0.4,2,0.6,1) forwards;
}
.animated-title .char:nth-child(1) { --x: -3vw; --y: -4vw; --r: -60deg; animation-delay: 0.05s; }
.animated-title .char:nth-child(2) { --x: 2vw; --y: -5vw; --r: 40deg; animation-delay: 0.15s; }
.animated-title .char:nth-child(3) { --x: -4vw; --y: 3vw; --r: 30deg; animation-delay: 0.25s; }
.animated-title .char:nth-child(4) { --x: 4vw; --y: -2vw; --r: -30deg; animation-delay: 0.35s; }
.animated-title .char:nth-child(5) { --x: -2vw; --y: 5vw; --r: 50deg; animation-delay: 0.45s; }
.animated-title .char:nth-child(6) { --x: 3vw; --y: 4vw; --r: -50deg; animation-delay: 0.55s; }
.animated-title .char:nth-child(7) { --x: -5vw; --y: -2vw; --r: 60deg; animation-delay: 0.65s; }
.animated-title .char:nth-child(8) { --x: 5vw; --y: 3vw; --r: -40deg; animation-delay: 0.75s; }
.animated-title .char:nth-child(9) { --x: 0vw; --y: -6vw; --r: 20deg; animation-delay: 0.85s; }
.animated-title .char:nth-child(10) { --x: 0vw; --y: 6vw; --r: -20deg; animation-delay: 0.95s; }

@keyframes fly-in {
  0% {
    opacity: 0;
    filter: blur(12px) brightness(1.8) drop-shadow(0 0 24px #b388ff);
    transform: translate(var(--x, 0), var(--y, 0)) scale(1.8) rotate(var(--r, 0deg));
  }
  60% {
    opacity: 1;
    filter: blur(2px) brightness(1.2) drop-shadow(0 0 12px #6ecbff);
    transform: translate(0, 0) scale(1.1) rotate(0deg);
  }
  100% {
    opacity: 1;
    filter: blur(0) brightness(1) drop-shadow(0 0 0 #fff0);
    transform: translate(0, 0) scale(1) rotate(0deg);
  }
}
