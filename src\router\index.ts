import ChatView from '@/views/chat/ChatPage.vue'
import NotFoundView from '@/views/error/NotFoundView.vue'
import NotPermissionView from '@/views/error/NotPermissionView.vue'
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory('/'),
  routes: [
    {
      path: '/',
      name: 'chat',
      component: ChatView,
    },
    {
      path: '/not-permission',
      name: 'notPermission',
      component: NotPermissionView,
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      component: NotFoundView
    }
  ],
})

export default router
