import NotFoundView from "@/views/ErrorView/NotFoundView.vue";
import HomeView from "@/views/HomeView/index.vue";
import VetAssistantView from "@/views/VetAssistantView/index.vue";
import VetChatView from "@/views/VetChatView/index.vue";
import VetOpenView from "@/views/VetOpenView/index.vue";
import {
  createRouter,
  createWebHistory,
  type RouteRecordRaw,
} from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "home",
    component: HomeView,
  },
  {
    path: "/vet-chat",
    name: "vet-chat",
    component: VetChatView,
  },
  {
    path: "/vet-assistant",
    name: "vet-assistant",
    component: VetAssistantView,
  },
  {
    path: "/vet-open",
    name: "vet-open",
    component: VetOpenView,
  },
  {
    path: "/:pathMatch(.*)*",
    name: "not-found",
    component: NotFoundView,
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
