<script lang="ts">
export default {
  name: "ChatView",
};
</script>
<script lang="ts" setup>
// import ChatHeader from "@/components/ChatHeader.vue";
import { CHAT_BACKGROUND_COLOR, MIDDLE_MARGIN } from "@/constants/styles";
</script>

<template>
  <div class="chat-view">
    <div class="chat-header">
      <ChatHeader />
    </div>
    <div class="chat-content">
      <div class="chat-slider">
        <div>
          <span>功能切换</span>
          <ul>
            <li><a href="/">首页</a></li>
            <li><a href="/products">产品</a></li>
            <li><a href="/about">关于我们</a></li>
          </ul>
        </div>
        <div>
          <span>历史对话</span>
          <ul>
            <li><a href="/">首页</a></li>
            <li><a href="/products">产品</a></li>
            <li><a href="/about">关于我们</a></li>
          </ul>
        </div>
      </div>
      <div class="chat-box"></div>
    </div>
  </div>
</template>
<style scoped>
.chat-view {
  background-color: v-bind(CHAT_BACKGROUND_COLOR);
  display: flex;
  flex-direction: column;
  height: 100dvh;
}

.chat-header {
  background-color: white;
  flex: 0;
  margin: v-bind(MIDDLE_MARGIN);
}

.chat-content {
  display: flex;
  flex: 1;
  margin: v-bind(MIDDLE_MARGIN);
  gap: 12px;
}

.chat-slider {
  background-color: white;
  width: 300px;
}

.chat-box {
  background-color: white;
  width: 100%;
}
</style>
