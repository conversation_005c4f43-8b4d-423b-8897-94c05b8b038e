<script setup lang="ts">
import AttachmentIcon from '@/components/icons/AttachmentIcon.vue';
import ImageIcon from '@/components/icons/ImageIcon.vue';
import LoadingSpinner from '@/components/icons/LoadingSpinner.vue';
import StopIcon from '@/components/icons/StopIcon.vue';
import SubmitArrowIcon from '@/components/icons/SubmitArrowIcon.vue';
import { buttonColor, chatInputBorderRadius } from '@/constants/styles';
import OcrService from '@/services/ocrService';
import { Close } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

// 图片上传状态类型
export type ImageUploadStatus = 'uploading' | 'success' | 'error';

// 图片附件接口
export interface ImageAttachment {
  name: string;
  ocrText?: string;
  size: number;
  status?: ImageUploadStatus; // 上传状态
}

// Props
interface Props {
  disabled?: boolean;
  isAiTyping?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  isAiTyping: false
});

// Emits
const emit = defineEmits<{
  sendMessage: [message: string, attachments?: ImageAttachment[]];
  stopResponse: [];
}>();

const senderValue = ref('');
const fileInputRef = ref<HTMLInputElement>();
const uploadedImages = ref<ImageAttachment[]>([]);

// 发送消息
const handleSendMessage = async () => {
  // 阻止事件冒泡和默认行为，防止触发输入框焦点
  const message = senderValue.value.trim();

  if (!message && uploadedImages.value.length === 0) {
    ElMessage.warning('请输入消息内容或上传图片');
    return;
  }

  if (props.disabled) {
    ElMessage.warning('AI正在回复中，请稍候...');
    return;
  }

  // 发送消息和附件
  emit('sendMessage', message || '根据上传的图片回答问题', uploadedImages.value.length > 0 ? uploadedImages.value : undefined);

  // 清空输入框和附件
  senderValue.value = '';
  uploadedImages.value = [];
};

// 处理附件上传
const handleAttachment = () => {
  if (!fileInputRef.value) {
    // 创建隐藏的文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = false;
    input.onchange = handleFileSelect;
    input.click();
  } else {
    fileInputRef.value.click();
  }
};

// 处理文件选择
const handleFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) return;

  const file = files[0];

  // 验证图片格式和大小
  const validation = OcrService.validateImage(file);
  if (!validation.valid) {
    ElMessage.error(validation.message || '图片验证失败');
    target.value = '';
    return;
  }

  // 立即创建图片附件对象并显示预览，状态为上传中
  const base64 = await OcrService.fileToBase64(file);
  const imageAttachment: ImageAttachment = {
    name: file.name,
    size: file.size,
    status: 'uploading'
  };

  // 添加到上传列表中
  uploadedImages.value.push(imageAttachment);
  const imageIndex = uploadedImages.value.length - 1;

  // 异步进行OCR识别
  try {
    const response = await OcrService.ocrText(base64);

    // OCR成功，更新状态为成功
    uploadedImages.value[imageIndex].status = 'success';
    uploadedImages.value[imageIndex].ocrText = response.result.text;
  } catch (ocrError) {
    console.error('OCR识别失败:', ocrError);
    // OCR失败，更新状态为错误
    uploadedImages.value[imageIndex].status = 'error';
  }

  // 清空input值，允许重复选择同一文件
  target.value = '';
};

// 移除上传的图片
const removeImage = (index: number) => {
  uploadedImages.value.splice(index, 1);
};

// 获取图片格式
const getImageFormat = (filename: string) => {
  const extension = filename.split('.').pop()?.toUpperCase();
  return extension || 'IMAGE';
};

// 格式化文件名显示（前4+...+后5）
const formatFileName = (filename: string) => {
  if (filename.length <= 12) {
    return filename;
  }

  // 显示前4个字符 + ... + 后5个字符
  const prefix = filename.substring(0, 3);
  const suffix = filename.substring(filename.length - 6);

  return prefix + '...' + suffix;
};

// 停止AI回复
const handleStopResponse = () => {
  emit('stopResponse');
};
</script>

<template>
  <div class="chat-input-wrapper" :style="{ '--chat-input-border-radius': chatInputBorderRadius }">
    <!-- 图片预览区域 -->
    <div v-if="uploadedImages.length > 0" class="image-preview-container">
      <div class="image-preview-grid">
        <div v-for="(image, index) in uploadedImages" :key="index" class="image-preview-item"
          :class="{ 'error-state': image.status === 'error' }">
          <div class="image-content">
            <div class="image-icon">
              <ImageIcon />
            </div>
            <div class="image-details">
              <div class="image-name">{{ formatFileName(image.name) }}</div>
              <div class="image-meta">
                {{ getImageFormat(image.name) }} · {{ (image.size / 1024).toFixed(1) }}KB
              </div>
            </div>
          </div>

          <!-- 上传状态覆盖层 -->
          <div v-if="image.status === 'uploading'" class="upload-overlay">
            <div class="loading-spinner-container">
              <LoadingSpinner />
            </div>
          </div>

          <el-button circle @click="removeImage(index)" class="remove-btn">
            <el-icon>
              <Close />
            </el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <MentionSender v-model="senderValue" variant="updown" :auto-size="{ minRows: 2, maxRows: 8 }"
      placeholder="给好兽医AI助手发送消息" @submit="handleSendMessage">
      <template #action-list>
        <!-- 上传图片OCR识别按钮 -->
        <el-button @mousedown.stop circle plain :color="buttonColor" :disabled="disabled" @click="handleAttachment()">
          <el-icon class="attachment-icon">
            <AttachmentIcon />
          </el-icon>
        </el-button>
        <!-- AI正在回复时显示停止按钮 -->
        <el-button @mousedown.stop v-if="isAiTyping" circle :color="buttonColor" @click="handleStopResponse()">
          <el-icon class="stop-icon">
            <StopIcon />
          </el-icon>
        </el-button>
        <!-- AI未回复时显示发送按钮 -->
        <el-button @mousedown.stop v-else circle :color="buttonColor" :disabled="disabled
          || (!senderValue.trim() && !uploadedImages.some(item => item.status === 'success'))
          || uploadedImages.some(item => item.status === 'uploading')" @click="handleSendMessage()">
          <el-icon class="submit-icon">
            <SubmitArrowIcon />
          </el-icon>
        </el-button>
      </template>
    </MentionSender>
    <div class="chat-input-remark">
      内容由AI生成，请仔细甄别
    </div>
  </div>
</template>

<style>
.chat-input-wrapper {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 100;
  padding: 6px 6px 2dvh 6px;
  border-radius: var(--chat-input-border-radius);
  box-shadow:
    0 -4px 20px rgba(0, 87, 255, 0.08),
    0 -2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 87, 255, 0.06);
  border-bottom: none;
  transition: all 0.3s ease;

  &:hover {
    box-shadow:
      0 -6px 30px rgba(0, 87, 255, 0.12),
      0 -4px 12px rgba(0, 0, 0, 0.06);
  }

  .el-sender {
    border-radius: var(--chat-input-border-radius);
    background: transparent;
    border: none;
    box-shadow: none !important;
  }

  /* 输入框样式优化 */
  .el-textarea__inner {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(0, 87, 255, 0.06);
    border-radius: 16px;
    padding: 14px 18px;
    font-size: 15px;
    line-height: 1.5;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }

  .el-textarea__inner:focus {
    border-color: rgba(0, 87, 255, 0.3);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(0, 87, 255, 0.1);
  }

  .el-textarea__inner::placeholder {
    color: #999;
    font-weight: 400;
  }
}

.chat-input-remark {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 12px;
  color: #666;
  background: rgba(0, 87, 255, 0.05);
  border-radius: 12px;
  margin-inline: 16px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 按钮容器样式优化 */
.el-sender__action-list {
  gap: 8px;
  padding: 0 16px;
}

/* 按钮基础样式优化 */
.el-button.is-circle {
  width: 36px;
  height: 36px;
  border-radius: 22px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.2);
  border: 2px solid rgba(0, 87, 255, 0.06);
}

.el-button.is-circle:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 87, 255, 0.3);
}

.el-button.is-circle:active {
  transform: translateY(0);
}

/* 上传按钮特殊样式 - 保持原背景色但添加点击反馈 */
.el-button.is-circle.is-plain {
  background-color: transparent;
  border-color: rgb(26 113 244);
  color: #0057ff;
}

.el-button.is-circle.is-plain:hover {
  background-color: transparent;
  border-color: rgb(26 113 244);
  color: #0057ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 87, 255, 0.25);
}

/* 上传按钮点击反馈效果 */
.el-button.is-circle.is-plain:active {
  transform: translateY(1px) scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 87, 255, 0.3);
  transition: all 0.1s ease;
}

/* 提交按钮图标样式优化 */
.submit-icon {
  font-weight: bold !important;
  font-size: 20px !important;
  color: white !important;
}

.submit-icon svg {
  stroke-width: 2.5 !important;
  font-weight: bold !important;
}

/* 附件按钮图标样式优化 */
.attachment-icon {
  font-size: 18px !important;
  color: rgb(26 113 244) !important;
  transition: all 0.2s ease;
}

.attachment-icon svg {
  stroke-width: 2 !important;
}

/* 停止按钮图标样式优化 */
.stop-icon {
  font-size: 18px !important;
  color: white !important;
}

.stop-icon svg {
  fill: currentColor !important;
}

/* 图片预览样式 - 现代化设计 */
.image-preview-container {
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.image-preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.image-preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  position: relative;
  transition: border-color 0.3s ease;
}

.image-preview-item.error-state {
  border-color: var(--el-color-danger-light-5);
  background: var(--el-color-danger-light-9);
}

.image-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.image-icon {
  color: var(--el-color-primary);
  font-size: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.image-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.image-name {
  font-size: 12px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
}

.remove-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 24px !important;
  height: 24px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #ff4757 !important;
  color: white !important;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

/* 上传状态覆盖层 - 现代化设计 */
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner-container {
  font-size: 20px;
  color: var(--el-color-primary);
}

/* 错误状态文本 */
.error-text {
  color: var(--el-color-danger);
  font-weight: 500;
}
</style>
