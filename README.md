# 好兽医AI助手聊天系统

<div align="center">
  <img src="public/logo.png" alt="好兽医AI助手" width="120" height="120">
  <h3>智能宠物健康管家</h3>
  <p>基于Vue 3 + TypeScript构建的现代化AI聊天系统</p>
</div>

## 📋 目录

- [系统访问流程](#系统访问流程)
- [系统介绍](#系统介绍)
- [功能特性](#功能特性)
- [技术架构](#技术架构)
- [开发指南](#开发指南)
- [API文档](#api文档)
- [部署说明](#部署说明)
- [常见问题](#常见问题)

## 🚀 系统访问流程

### 1. 获取临时API密钥

在访问聊天系统之前，需要先调用API获取临时访问密钥：

**接口信息：**

- **URL**: `POST https://open.haoshouyi.com/api/v1/temporary-api-keys/`
- **请求头**:
  ```http
  Authorization: Bearer {your_real_api_key}
  Content-Type: application/json
  ```
- **请求参数**:
  ```json
  {
    "name": "临时密钥名称",
    "description": "密钥用途描述",
    "expires_hours": 24,
    "max_usage_count": 100
  }
  ```

**响应示例：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "api_key": "tt-w4VcRSpszepAEqumQBaELrezp5CbDvIVk7x0T2QaaNGgsIDs",
    "expires_at": "2024-01-02T10:00:00Z",
    "max_usage_count": 100,
    "current_usage_count": 0
  }
}
```

### 2. 构造访问参数

将获取到的临时API密钥按以下格式组织：

```json
{
  "api_key": "tt-w4VcRSpszepAEqumQBaELrezp5CbDvIVk7x0T2QaaNGgsIDs",
  "report_id": 221
}
```

**参数说明：**

- `api_key`: 从步骤1获取的临时API密钥
- `report_id`: 宠物检查报告的唯一标识符

### 3. Base64编码

将上述JSON数据进行Base64编码处理：

```javascript
// JavaScript示例
const params = {
  api_key: 'tt-w4VcRSpszepAEqumQBaELrezp5CbDvIVk7x0T2QaaNGgsIDs',
  report_id: 221,
}
const encodedParams = btoa(JSON.stringify(params))
console.log(encodedParams)
// 输出: eyJhcGlfa2V5IjoidHQtdzRWY1JTcHN6ZXBBRXF1bVFCYUVMcmV6cDVDYkR2SVZrN3gwVDJRYWFOR2dzSURzIiwicmVwb3J0X2lkIjoyMjF9
```

### 4. 访问系统

使用编码后的数据构造访问链接：

```
https://chat.haoshouyi.com/?q={base64_encoded_data}
```

**完整示例：**

```
https://chat.haoshouyi.com/?q=eyJhcGlfa2V5IjoidHQtdzRWY1JTcHN6ZXBBRXF1bVFCYUVMcmV6cDVDYkR2SVZrN3gwVDJRYWFOR2dzSURzIiwicmVwb3J0X2lkIjoyMjF9
```

### 错误处理

**常见错误及解决方案：**

| 错误码 | 错误信息     | 解决方案                            |
| ------ | ------------ | ----------------------------------- |
| 401    | API密钥无效  | 检查API密钥是否正确，是否已过期     |
| 403    | 访问被拒绝   | 检查API密钥权限，确认使用次数未超限 |
| 404    | 报告不存在   | 确认report_id是否正确               |
| 429    | 请求过于频繁 | 稍后重试，检查API调用频率           |

## 💡 系统介绍

好兽医AI助手是一款专为宠物健康诊断设计的智能聊天系统，基于先进的人工智能技术，为宠物主人和兽医提供专业的健康咨询服务。

### 核心优势

- **🎯 专业诊断**: 基于大量宠物医疗数据训练，提供准确的健康分析
- **📱 移动优先**: 响应式设计，完美适配各种移动设备
- **⚡ 实时交互**: 支持流式对话，提供流畅的用户体验
- **🖼️ 图像识别**: 支持上传宠物照片和检查报告，AI自动识别分析
- **🔒 安全可靠**: 采用临时API密钥机制，确保数据安全
- **🌐 云端部署**: 基于云服务架构，稳定可靠

### 使用场景

1. **健康咨询**: 宠物主人可以描述宠物症状，获得初步诊断建议
2. **报告解读**: 上传宠物检查报告，AI帮助解读检查结果
3. **预防指导**: 获取宠物日常护理和疾病预防建议
4. **紧急处理**: 紧急情况下的应急处理指导
5. **专业交流**: 兽医之间的专业知识交流平台

### 界面说明

#### 主界面布局

- **顶部标题栏**: 显示"好兽医AI助手"品牌标识和副标题
- **聊天区域**: 显示对话历史，支持滚动查看
- **输入区域**: 文本输入框和功能按钮
- **底部提示**: 显示AI生成内容的免责声明

#### 消息类型

- **用户消息**: 蓝色气泡，显示在右侧
- **AI回复**: 白色气泡，显示在左侧，包含思考过程
- **系统提示**: 橙色提示框，显示重要信息

#### 特殊功能

- **思考过程**: AI回复包含可折叠的思考过程，展示推理逻辑
- **图片上传**: 支持拖拽或点击上传图片，自动OCR识别
- **流式输出**: AI回复采用打字机效果，实时显示生成内容
- **中断保护**: 切换标签页时自动暂停输出，避免内容丢失

## ✨ 功能特性

### 🤖 AI对话功能

- **智能问答**: 基于自然语言处理的智能对话系统
- **上下文理解**: 支持多轮对话，理解对话上下文
- **专业知识**: 内置丰富的宠物医疗知识库
- **个性化回复**: 根据宠物种类和症状提供针对性建议

### 📸 图像处理功能

- **OCR文字识别**: 自动识别检查报告中的文字信息
- **图像上传**: 支持多种图片格式（JPG、PNG、WebP等）
- **拖拽上传**: 便捷的拖拽上传体验
- **上传状态**: 实时显示上传进度和状态

### 💬 聊天体验功能

- **流式输出**: 实时显示AI生成内容，提供流畅体验
- **思考过程**: 展示AI的推理过程，增加透明度
- **消息历史**: 自动保存对话历史，支持翻页查看
- **响应式设计**: 适配各种屏幕尺寸和设备

### 🔧 系统功能

- **会话管理**: 自动管理用户会话，保持对话连续性
- **错误处理**: 完善的错误处理机制，提供友好的错误提示
- **性能优化**: 虚拟滚动、懒加载等性能优化技术
- **安全防护**: API密钥验证、请求频率限制等安全措施

## 🏗️ 技术架构

### 前端技术栈

| 技术               | 版本    | 用途       |
| ------------------ | ------- | ---------- |
| Vue 3              | ^3.5.17 | 前端框架   |
| TypeScript         | ~5.8.0  | 类型系统   |
| Element Plus       | ^2.10.4 | UI组件库   |
| Vue Element Plus X | ^1.3.0  | 扩展组件库 |
| Vue Router         | ^4.5.1  | 路由管理   |
| Pinia              | ^3.0.3  | 状态管理   |
| Axios              | ^1.10.0 | HTTP客户端 |
| Vite               | ^7.0.0  | 构建工具   |

### 核心架构

```
src/
├── components/          # 公共组件
│   ├── header/         # 头部组件
│   ├── icons/          # 图标组件
│   └── loading/        # 加载组件
├── constants/          # 常量定义
├── services/           # 业务服务
│   ├── chatService.ts  # 聊天服务
│   ├── conversationService.ts # 会话服务
│   └── ocrService.ts   # OCR服务
├── utils/              # 工具函数
│   ├── api.ts          # API封装
│   ├── routeUtils.ts   # 路由工具
│   └── storageUtils.ts # 存储工具
├── views/              # 页面组件
│   ├── chat/           # 聊天页面
│   └── error/          # 错误页面
└── App.vue             # 根组件
```

### 数据流架构

```mermaid
graph TD
    A[用户输入] --> B[ChatInput组件]
    B --> C[ChatPage组件]
    C --> D[ChatService]
    D --> E[API服务器]
    E --> F[AI模型]
    F --> G[流式响应]
    G --> H[ChatContent组件]
    H --> I[用户界面]
```

### 核心服务

#### ChatService

- 负责与AI服务器的通信
- 实现流式对话功能
- 处理消息发送和接收
- 管理连接状态和错误处理

#### ConversationService

- 管理用户会话
- 处理会话创建和验证
- 维护会话状态

#### OcrService

- 处理图片上传
- 实现OCR文字识别
- 管理图片处理状态

## 🛠️ 开发指南

### 环境要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **现代浏览器**: Chrome 88+, Firefox 85+, Safari 14+

### 快速开始

1. **克隆项目**

   ```bash
   git clone <repository-url>
   cd vet_chat_ui
   ```

2. **安装依赖**

   ```bash
   npm install
   # 或
   yarn install
   ```

3. **配置环境变量**

   ```bash
   # 复制环境变量模板
   cp .env.example .env.local

   # 编辑环境变量
   vim .env.local
   ```

4. **启动开发服务器**

   ```bash
   npm run dev
   # 或
   yarn dev
   ```

5. **访问应用**
   ```
   http://localhost:3000
   ```

### 开发脚本

```bash
# 开发模式
npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 代码格式化
npm run format

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 项目配置

#### Vite配置 (vite.config.ts)

```typescript
export default defineConfig({
  build: {
    outDir: 'vet_chat_ui', // 构建输出目录
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://open.haoshouyi.com/api/v1',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
})
```

#### TypeScript配置

- 严格模式启用
- 支持Vue 3 Composition API
- 路径别名配置 (`@` -> `src/`)

### 代码规范

#### 命名规范

- **组件**: PascalCase (如: `ChatInput.vue`)
- **文件**: camelCase (如: `chatService.ts`)
- **变量**: camelCase (如: `isAiTyping`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)

#### 组件规范

- 使用 Composition API
- TypeScript 类型定义
- Props 和 Emits 明确声明
- 响应式数据使用 ref/reactive

#### 样式规范

- 使用 CSS 变量
- 响应式设计优先
- 组件样式作用域化
- 统一的设计系统

## 📚 API文档

### 核心接口

#### 1. 发送消息接口

**接口地址**: `POST /api/system-agents/diagnosis`

**请求头**:

```http
Content-Type: application/json
Accept: text/event-stream
Authorization: Bearer {api_key}
```

**请求参数**:

```json
{
  "messages": [
    {
      "role": "user",
      "content": "宠物症状描述或检查报告内容"
    }
  ],
  "conversation_id": "会话ID",
  "stream": true
}
```

**响应格式** (Server-Sent Events):

```
data: {"content": "AI回复内容", "thinking": "思考过程", "finished": false}
data: {"event": "end"}
```

#### 2. 获取聊天历史

**接口地址**: `GET /api/conversations/{conversation_id}/messages`

**请求参数**:

- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)

**响应格式**:

```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 123,
        "role": 1,
        "content": "消息内容",
        "created_at": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

#### 3. OCR文字识别

**接口地址**: `POST /api/ocr/extract`

**请求参数**:

```json
{
  "image_base64": "base64编码的图片数据"
}
```

**响应格式**:

```json
{
  "code": 200,
  "data": {
    "text": "识别出的文字内容",
    "confidence": 0.95
  }
}
```

### 错误码说明

| 错误码 | 说明           | 处理方式               |
| ------ | -------------- | ---------------------- |
| 200    | 成功           | 正常处理               |
| 400    | 请求参数错误   | 检查请求参数格式       |
| 401    | 认证失败       | 检查API密钥            |
| 403    | 权限不足       | 检查API密钥权限        |
| 404    | 资源不存在     | 检查请求路径和参数     |
| 429    | 请求过于频繁   | 实施请求限流           |
| 500    | 服务器内部错误 | 稍后重试或联系技术支持 |

## 🚀 部署说明

### 构建生产版本

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 构建产物位于 vet_chat_ui/ 目录
ls vet_chat_ui/
```

### Docker部署

1. **创建Dockerfile**:

```dockerfile
FROM nginx:alpine

# 复制构建产物
COPY vet_chat_ui/ /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

2. **创建nginx.conf**:

```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass https://open.haoshouyi.com/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

3. **构建和运行**:

```bash
# 构建镜像
docker build -t vet-chat-ui .

# 运行容器
docker run -d -p 80:80 vet-chat-ui
```

### CDN部署

推荐使用以下CDN服务：

- **阿里云OSS + CDN**
- **腾讯云COS + CDN**
- **AWS S3 + CloudFront**
- **Vercel** (推荐用于快速部署)

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
# API基础URL
VITE_API_BASE_URL=https://open.haoshouyi.com/api/v1

# 应用环境
VITE_APP_ENV=production

# 是否启用API日志
VITE_ENABLE_API_LOGS=false
```

## ❓ 常见问题

### Q1: 如何获取API密钥？

**A**: 请联系好兽医平台管理员获取正式的API密钥，用于生成临时访问密钥。

### Q2: 为什么切换浏览器标签页后AI回复中断了？

**A**: 这是正常的保护机制。由于SSE连接在标签页切换时会中断，系统会自动暂停输出并提示用户。回到页面后需要重新发送消息。

### Q3: 上传的图片为什么识别不准确？

**A**: 请确保：

- 图片清晰度足够高
- 文字部分没有遮挡
- 图片格式为JPG、PNG或WebP
- 图片大小不超过10MB

### Q4: 如何查看AI的思考过程？

**A**: AI回复消息中包含可折叠的"思考过程"部分，点击展开/折叠按钮即可查看AI的推理逻辑。

### Q5: 系统支持哪些浏览器？

**A**: 支持所有现代浏览器：

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### Q6: 如何报告问题或建议？

**A**: 请通过以下方式联系我们：

- 技术支持邮箱: <EMAIL>
- 官方网站: https://www.haoshouyi.com
- 在线客服: 工作日 9:00-18:00

### Q7: 数据安全如何保障？

**A**: 系统采用多重安全措施：

- 临时API密钥机制，限制访问时间和次数
- HTTPS加密传输
- 不存储用户敏感信息
- 定期安全审计

### Q8: 系统有使用限制吗？

**A**: 是的，主要限制包括：

- API调用频率限制
- 单次对话长度限制
- 图片上传大小限制
- 临时密钥有效期限制

---

<div align="center">
  <p>© 2024 好兽医AI助手. All rights reserved.</p>
  <p>
    <a href="https://www.haoshouyi.com">官方网站</a> |
    <a href="mailto:<EMAIL>">技术支持</a> |
    <a href="https://www.haoshouyi.com/privacy">隐私政策</a>
  </p>
</div>
