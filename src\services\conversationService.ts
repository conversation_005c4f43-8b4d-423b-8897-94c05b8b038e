import { CONVERSATION_TITLE, CONVERSATION_TYPE } from '@/constants/constant';
import { getAuthorizationHeader, getReportId, replaceToNotPermission } from '@/utils/routeUtils';
import { getConversationId, setConversationId } from '@/utils/storageUtils';
import axios from 'axios';
import type { ChatMessage } from './chatService';

/**
 * 会话信息接口
 */
export interface ConversationInfo {
  title: string;
  conversation_id: string;
  conversation_type: number;
  config: unknown;
  context_window_size: number;
  max_messages: number;
  expires_in_hours: number | null;
  id: number;
  status: number;
  user_id: number;
  tenant_id: number | null;
  primary_agent_id: number | null;
  last_activity_at: string;
  expires_at: string | null;
  message_count: number;
  total_tokens: number;
  created_at: string;
  updated_at: string;
  primary_agent: unknown;
  recent_messages: ChatMessage[];
  context_summary: string | null;
}

/**
 * 会话管理类
 */
export class ConversationService {
  /**
   * 获取会话信息
   * @param conversationId 会话ID（可选）
   * @returns 会话信息
   */
  static async createConversation() {
    const response = await axios.post('api/conversations/',
      {
        title: CONVERSATION_TITLE,
        conversation_type: CONVERSATION_TYPE,
        extend: {
          report_id: getReportId(),
        }
      },
      {
        headers: {
          'Authorization': getAuthorizationHeader()
        }
      }
    );
    return response.data.data;
  }

  static async checkConversation(): Promise<void> {
    // 添加会话 ID
    const conversationId = getConversationId()
    if (!conversationId) {
      const conversation = await this.createConversation()
      setConversationId(conversation.conversation_id)
    }
  }

  static async regenerateConversation(): Promise<void> {
    try {
      // 添加会话 ID
      const conversation = await this.createConversation()
      setConversationId(conversation.conversation_id)
    } catch (e) {
      console.log(e);
      replaceToNotPermission()
    }
  }
}

export default ConversationService;
