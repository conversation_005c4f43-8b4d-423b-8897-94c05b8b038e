/* 保留 vet-chat-view、chat-title、title-left、title-right、trial-button 及相关动画，删除未被用到的多余样式 */
.vet-chat-view {
  background: url("/home/<USER>") no-repeat center center;
  background-size: cover;
  height: 100dvh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
}
.vet-chat-view > h1 {
  font-size: var(--h1-font-middle-size);
  letter-spacing: 0.6dvw;
}
.vet-chat-view > p {
  font-size: 1dvw;
  line-height: 1.5dvw;
  letter-spacing: 0.15dvw;
  width: 40%;
  animation: slide-in-left 1.2s cubic-bezier(0.4,2,0.6,1) 0.6s both;
}
.chat-title .title-left {
  display: inline-block;
  opacity: 0;
  animation: slide-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 0.1s both;
}
.chat-title .title-right {
  display: inline-block;
  opacity: 0;
  animation: slide-in-down 1.2s cubic-bezier(0.4,2,0.6,1) 0.5s both;
}
@keyframes slide-in-down {
  0% {
    opacity: 0;
    transform: translateY(-6vw) scale(1.1);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}
@keyframes slide-in-left {
  0% {
    opacity: 0;
    transform: translateX(-8vw) scale(1.05);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}
@keyframes slide-in-right {
  0% {
    opacity: 0;
    transform: translateX(8vw) scale(1.05);
    filter: blur(8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}
.trial-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.2dvw;
  border-radius: 2vw;
  border: 3px solid white;
  color: white;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.3) 0%,
    rgba(0, 200, 255, 0.3) 50%,
    rgba(0, 255, 200, 0.3) 100%
  );
  backdrop-filter: blur(10px);
  box-shadow: 0 0 15px rgba(0, 200, 255, 0.5);
  transition: all 0.3s ease;
  margin-block: 4dvh 10dvh;
  animation: slide-in-right 1.2s cubic-bezier(0.4,2,0.6,1) 0.9s both;
}
.trial-button:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 150, 255, 0.5) 0%,
    rgba(0, 255, 255, 0.5) 50%,
    rgba(0, 255, 150, 0.5) 100%
  );
  box-shadow: 0 0 20px rgba(0, 200, 255, 0.8);
  transform: translateY(-2px);
}
