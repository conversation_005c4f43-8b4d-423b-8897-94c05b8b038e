<template>
  <span class="custom-icon" v-html="iconSvg"></span>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants/icons';

const iconSvg = ICONS.SUBMIT_ARROW;
</script>

<style scoped>
.custom-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

.custom-icon :deep(svg) {
  width: 100%;
  height: 100%;
  fill: currentColor;
}
</style>
