<template>
  <header class="header">
    <!-- 背景装饰元素 -->
    <div class="bg-decoration">
      <div class="floating-paw paw-1">🐾</div>
      <div class="floating-paw paw-2">🐾</div>
      <div class="floating-paw paw-3">🐾</div>
      <div class="tech-grid"></div>
    </div>

    <!-- 主要内容 -->
    <div class="header-content">
      <div class="logo-container">
        <div class="logo-glow"></div>
        <img src="/logo.png" alt="logo" class="logo" />
        <div class="pulse-ring"></div>
      </div>

      <div class="title-section">
        <h1 class="title">
          <span class="title-main">好兽医AI助手</span>
        </h1>
        <div class="subtitle">智能宠物健康管家</div>
      </div>
    </div>

    <!-- 底部光效 -->
    <div class="bottom-glow"></div>
  </header>
</template>

<script setup lang="ts">
// 头部组件无需额外逻辑
</script>

<script lang="ts">
export default {
  name: 'AppHeader',
};
</script>

<style scoped>
/* 关键帧动画定义 */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }

  50% {
    transform: translateY(-10px) rotate(5deg);
  }

  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 0.4;
  }

  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }

  100% {
    opacity: 0.5;
  }
}

@keyframes statusBlink {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0.7);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(0, 255, 128, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 128, 0);
  }
}

/* 主要样式 */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 40%, #001f66 100%);
  color: white;
  box-shadow: 0 4px 20px rgba(0, 87, 255, 0.3);
  border-bottom-left-radius: 24px;
  border-bottom-right-radius: 24px;
  padding: 0 20px;
  overflow: hidden;
  z-index: 10;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, rgba(255, 255, 255, 0.07) 1px, transparent 1px),
    linear-gradient(0deg, rgba(255, 255, 255, 0.07) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.floating-paw {
  position: absolute;
  font-size: 24px;
  opacity: 0.2;
  animation: float 6s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(0, 87, 255, 0.8));
  z-index: 1;
}

.paw-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  transform: rotate(-15deg);
}

.paw-2 {
  top: 40%;
  right: 15%;
  animation-delay: 2s;
  transform: rotate(25deg);
}

.paw-3 {
  bottom: 20%;
  left: 25%;
  animation-delay: 4s;
  transform: rotate(-5deg);
}

.bottom-glow {
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 15px;
  background: linear-gradient(to top, rgba(0, 87, 255, 0.6), transparent);
  filter: blur(5px);
  z-index: 2;
}

/* 主要内容 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 1200px;
  position: relative;
  z-index: 5;
}

/* Logo 样式 */
.logo-container {
  position: relative;
  margin-right: 15px;
}

.logo {
  padding-top: 8px;
  height: 54px;
  width: 54px;
  border-radius: 12px;
  object-fit: fill;
  box-shadow: 0 0 15px rgba(0, 87, 255, 0.5);
  background: transparent;
  position: relative;
  z-index: 3;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background: radial-gradient(circle, rgba(0, 87, 255, 0.8) 0%, rgba(0, 87, 255, 0) 70%);
  border-radius: 50%;
  z-index: 2;
  animation: glow 3s infinite ease-in-out;
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 55px;
  height: 55px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  z-index: 1;
  animation: pulse 3s infinite;
}

/* 标题样式 */
.title-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  letter-spacing: 1.5px;
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: linear-gradient(90deg, #ffffff 0%, #e0e9ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 87, 255, 0.3);
}

.title-main {
  font-weight: 800;
}

.subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 2px;
  letter-spacing: 0.5px;
}


/* 响应式设计 */
@media (max-width: 600px) {
  .header {
    height: 70px;
    padding: 0 15px;
  }

  .title {
    font-size: 18px;
  }

  .subtitle {
    font-size: 10px;
  }

  .logo {
    padding-top: 6px;
    height: 48px;
    width: 48px;
  }

}

@media (max-width: 375px) {
  .header {
    height: 60px;
    padding: 0 10px;
  }

  .title {
    font-size: 16px;
  }

  .subtitle {
    display: none;
  }

  .logo {
    padding-top: 4px;
    height: 42px;
    width: 42px;
  }

  .floating-paw {
    font-size: 18px;
  }
}
</style>
